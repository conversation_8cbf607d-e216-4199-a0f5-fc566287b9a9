using UnityEngine;
using UnityEngine.EventSystems;
using System;
using TMPro;
using UnityEngine.UI;

public class ThingEntryVisual : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler, IPointerDownHandler
{
    private ThingData _data;
    public ThingData Data
    {
        get
        {
            return _data;
        }
        set
        {
            if (_data != value)
            {
                _data = value;
                DataChanged?.Invoke();
            }
        }
    }
    public Action DataChanged;
    private TextMeshProUGUI NameText;
    private TextMeshProUGUI CountText;
    private Image Background;
    private GameObject NewIcon;

    private bool _isSelected;
    public bool IsSelected
    {
        get { return _isSelected; }
        set
        {
            if (_isSelected != value)
            {
                _isSelected = value;
                UpdateStateVisuals();
            }
        }
    }

    private bool _isHovered;
    public bool IsHovered
    {
        get { return _isHovered; }
        set
        {
            if (_isHovered != value)
            {
                _isHovered = value;
                UpdateStateVisuals();
            }
        }
    }

    public void UpdateVisuals()
    {
        // Update the visuals based on the data
        UpdateTextVisuals();
        NewIcon.SetActive(Data.ingredients.Count == 0);
        UpdateStateVisuals();
    }
    public void UpdateTextVisuals()
    {
        // Update the visuals based on the data
        NameText.text = Data.text;
        CountText.text = Data.ingredients.Count.ToString();
    }
    public void UpdateStateVisuals()
    {
        // Update the visuals based on the state
        if (IsSelected && IsHovered)
        {
            // Add your hover+selection logic here
            Background.color = new Color(0.18f, 0.23f, 0.35f, 1f);
            NameText.color = Color.white;
        }
        else if (IsSelected)
        {
            // Add your selection logic here
            Background.color = new Color(0.11f, 0.14f, 0.22f, 1f);
            NameText.color = Color.white;
        }
        else if (IsHovered)
        {
            // Add your hover logic here
            Background.color = new Color(0.07f, 0.1f, 0.15f, 0.50f);
            NameText.color = new Color(0.93f, 0.95f, 0.98f, 1f);
        }
        else
        {
            // Add your default state logic here
            Background.color = new Color(0.07f, 0.1f, 0.15f, 0.25f);
            NameText.color = new Color(0.93f, 0.95f, 0.98f, 1f);
        }
    }

    // Events for hover and click interactions
    public Action OnHoverEnter;
    public Action OnHoverLeave;
    public Action OnClickDown;

    // Unity Event Interface Implementations
    public void OnPointerEnter(PointerEventData eventData)
    {
        IsHovered = true;
        OnHoverEnter?.Invoke();
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        IsHovered = false;
        OnHoverLeave?.Invoke();
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        IsSelected = !IsSelected;
        OnClickDown?.Invoke();
    }

    public class Builder
    {
        public ThingData data;
        public GameObject container;

        public Builder(ThingData data, GameObject container)
        {
            this.data = data;
            this.container = container;
        }

        public ThingEntryVisual Build()
        {
            if (this.data == null)
            {
                this.data = new ThingData("");
            }
            GameObject thingEntryObject = Instantiate(Game.Instance.thingVisualObjectPrefab, container.transform);
            ThingEntryVisual thingEntry = thingEntryObject.GetComponent<ThingEntryVisual>();
            thingEntry.Data = this.data;

            thingEntry.NameText = thingEntryObject.transform.Find("NameText").GetComponent<TextMeshProUGUI>();
            thingEntry.CountText = thingEntryObject.transform.Find("CountText").GetComponent<TextMeshProUGUI>();
            thingEntry.NewIcon = thingEntryObject.transform.Find("NewIcon").gameObject;
            thingEntry.Background = thingEntryObject.GetComponent<Image>();

            thingEntry.UpdateVisuals();

            // Example: Hook up event callbacks (optional)
            // thingEntry.OnHoverEnter += () => Debug.Log($"Hovered over: {thingEntry.Data.text}");
            // thingEntry.OnHoverLeave += () => Debug.Log($"Stopped hovering: {thingEntry.Data.text}");
            // thingEntry.OnClickDown += () => Debug.Log($"Clicked: {thingEntry.Data.text}");

            return thingEntry;
        }
    }
}
