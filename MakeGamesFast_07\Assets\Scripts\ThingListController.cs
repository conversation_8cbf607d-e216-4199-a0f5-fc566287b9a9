using System;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;
using Unity.VisualScripting;

public class ThingListController : MonoBehaviourSingleton<ThingListController>
{
    // runtime
    [Required] public VariableGridLayout ContentContainer;
    [Required] public ThingDatabase thingDatabase;
    public ThingEntryVisual FirstSelectedEntry => _firstSelectedEntry;
    private ThingEntryVisual _firstSelectedEntry;
    public ThingEntryVisual SecondSelectedEntry => _secondSelectedEntry;
    private ThingEntryVisual _secondSelectedEntry;
    public Action OnSelectionChangedCallback;

    public List<ThingEntryVisual> ThingEntryVisuals = new List<ThingEntryVisual>();

    void Awake()
    {
        _firstSelectedEntry = null;
        _secondSelectedEntry = null;

        thingDatabase.OnDatabaseChanged += () => Populate(thingDatabase.entries);
        Populate(thingDatabase.entries);
    }

    /// <summary>
    /// Populate the list with the given data.
    /// Clears the old items and adds new ones.
    /// </summary>
    public void Populate(List<ThingData> thingDatas)
    {
        ThingEntryVisual[] children = ContentContainer.transform.GetComponentsInChildren<ThingEntryVisual>();

        foreach (ThingEntryVisual child in children)
        {
            if (child != ContentContainer.transform)
            {
                child.OnClickDown = null;
                Destroy(child.gameObject);
            }
        }

        ContentContainer.BeginLayoutUpdate();

        // Clear selections when repopulating
        _firstSelectedEntry = null;
        _secondSelectedEntry = null;

        foreach (ThingData thingData in thingDatas)
        {
            // clone your UXML <ThingEntry>
            ThingEntryVisual thingEntry = new ThingEntryVisual.Builder(thingData, ContentContainer.gameObject).Build();

            // hook up selection callback
            thingEntry.OnClickDown += () => OnEntrySelectionChanged(thingEntry);

            // add to scrollable list
            ThingEntryVisuals.Add(thingEntry);
        }

        // Notify that selection has changed after populating
        OnSelectionChangedCallback?.Invoke();
        ContentContainer.EndLayoutUpdate();
    }

    public void OnEntrySelectionChanged(ThingEntryVisual thingEntry)
    {
        // Handle selection logic based on current state and newly clicked entry
        bool isNewlySelected = thingEntry.IsSelected;

        if (isNewlySelected)
        {
            // Object was just selected
            HandleEntrySelected(thingEntry);
        }
        else
        {
            // Object was just unselected
            HandleEntryUnselected(thingEntry);
        }

        // Notify that selection has changed
        OnSelectionChangedCallback?.Invoke();
    }

    private void HandleEntrySelected(ThingEntryVisual thingEntry)
    {
        // Case 1: Neither first nor second selected - take first slot
        if (_firstSelectedEntry == null && _secondSelectedEntry == null)
        {
            _firstSelectedEntry = thingEntry;
        }
        // Case 2: First selected, second not selected - take second slot
        else if (_firstSelectedEntry != null && _secondSelectedEntry == null)
        {
            _secondSelectedEntry = thingEntry;
        }
        // Case 3: Both first and second selected - second becomes first, new takes second
        else if (_firstSelectedEntry != null && _secondSelectedEntry != null)
        {
            // Unselect the current first entry
            _firstSelectedEntry.IsSelected = false;

            // Move second to first, new entry to second
            _firstSelectedEntry = _secondSelectedEntry;
            _secondSelectedEntry = thingEntry;
        }
        // Case 4: Only second selected (shouldn't happen in normal flow, but handle it)
        else if (_firstSelectedEntry == null && _secondSelectedEntry != null)
        {
            _firstSelectedEntry = thingEntry;
        }
    }

    private void HandleEntryUnselected(ThingEntryVisual thingEntry)
    {
        // Remove from first slot if it matches
        if (_firstSelectedEntry == thingEntry)
        {
            _firstSelectedEntry = null;

            // If second exists, move it to first
            if (_secondSelectedEntry != null)
            {
                _firstSelectedEntry = _secondSelectedEntry;
                _secondSelectedEntry = null;
            }
        }
        // Remove from second slot if it matches
        else if (_secondSelectedEntry == thingEntry)
        {
            _secondSelectedEntry = null;
        }
    }
}
